# Basic test script for Unity Symbol Analyzer

Write-Host "Unity Symbol Analyzer - Basic Test" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green

# Build the project first
Write-Host "Building project..." -ForegroundColor Yellow
dotnet build --configuration Release

if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed"
    exit 1
}

Write-Host "Build successful!" -ForegroundColor Green

# Test 1: Help command
Write-Host "`nTest 1: Help command" -ForegroundColor Cyan
dotnet run -- --help

# Test 2: Invalid solution file
Write-Host "`nTest 2: Invalid solution file (should fail gracefully)" -ForegroundColor Cyan
dotnet run -- -s "nonexistent.sln" -f "test.cs" -l 1 -c 1

# Test 3: Test with sample data (if available)
$sampleSln = "Tests/TestData/Sample.sln"
$sampleFile = "Sample.cs"

if (Test-Path $sampleSln) {
    Write-Host "`nTest 3: Sample data analysis" -ForegroundColor Cyan
    Write-Host "Analyzing PlayerController class definition..." -ForegroundColor Yellow
    
    # Try to analyze the PlayerController class name at line 6, column 18
    dotnet run -- -s $sampleSln -f $sampleFile -l 6 -c 18 -v
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Sample analysis completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Sample analysis failed (this is expected for the test data)" -ForegroundColor Yellow
    }
} else {
    Write-Host "`nTest 3: Skipped (no sample data found)" -ForegroundColor Yellow
}

Write-Host "`nBasic tests completed!" -ForegroundColor Green
Write-Host "`nTo test with a real Unity project:" -ForegroundColor Cyan
Write-Host "1. Open your Unity project" -ForegroundColor White
Write-Host "2. Go to Assets -> Open C# Project (to generate .sln file)" -ForegroundColor White
Write-Host "3. Run: dotnet run -- -s ""YourProject.sln"" -f ""Assets/Scripts/YourScript.cs"" -l [line] -c [column]" -ForegroundColor White
