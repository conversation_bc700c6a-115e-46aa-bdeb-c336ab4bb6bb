<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <!-- OmniSharp Core Dependencies -->
    <ProjectReference Include="../../github/omnisharp-roslyn/src/OmniSharp.Host/OmniSharp.Host.csproj" />
    <ProjectReference Include="../../github/omnisharp-roslyn/src/OmniSharp.LanguageServerProtocol/OmniSharp.LanguageServerProtocol.csproj" />
    <ProjectReference Include="../../github/omnisharp-roslyn/src/OmniSharp.Roslyn.CSharp/OmniSharp.Roslyn.CSharp.csproj" />
    <ProjectReference Include="../../github/omnisharp-roslyn/src/OmniSharp.MSBuild/OmniSharp.MSBuild.csproj" />

    <!-- Additional packages -->
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

</Project>
