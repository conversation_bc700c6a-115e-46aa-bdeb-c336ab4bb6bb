using CommandLine;
using UnitySymbolAnalyzer.Core;
using UnitySymbolAnalyzer.Models;

namespace UnitySymbolAnalyzer;

public class Options
{
    [Option('s', "solution", Required = true, HelpText = "Path to Unity .sln file")]
    public string SolutionPath { get; set; } = string.Empty;

    [Option('f', "file", Required = true, HelpText = "Path to C# file (relative to solution)")]
    public string FilePath { get; set; } = string.Empty;

    [Option('l', "line", Required = true, HelpText = "Line number (1-based)")]
    public int Line { get; set; }

    [Option('c', "column", Required = true, HelpText = "Column number (1-based)")]
    public int Column { get; set; }

    [Option('v', "verbose", Required = false, HelpText = "Enable verbose output")]
    public bool Verbose { get; set; }

    [Option('o', "output", Required = false, HelpText = "Output format (json|text)", Default = "text")]
    public string OutputFormat { get; set; } = "text";
}

class Program
{
    static async Task<int> Main(string[] args)
    {
        return await Parser.Default.ParseArguments<Options>(args)
            .MapResult(async (Options opts) => await RunAnalysis(opts),
                      errs => Task.FromResult(1));
    }

    static async Task<int> RunAnalysis(Options options)
    {
        try
        {
            if (options.Verbose)
            {
                Console.WriteLine($"Analyzing solution: {options.SolutionPath}");
                Console.WriteLine($"Target location: {options.FilePath}:{options.Line}:{options.Column}");
            }

            var analyzer = new UnityProjectAnalyzer();
            await analyzer.LoadSolutionAsync(options.SolutionPath);

            var result = await analyzer.AnalyzeSymbolAtPositionAsync(
                options.FilePath, 
                options.Line - 1, // Convert to 0-based
                options.Column - 1); // Convert to 0-based

            if (result == null)
            {
                Console.WriteLine("No symbol found at the specified position.");
                return 1;
            }

            if (options.OutputFormat.ToLower() == "json")
            {
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(result, Newtonsoft.Json.Formatting.Indented);
                Console.WriteLine(json);
            }
            else
            {
                Console.WriteLine($"Symbol: {result.SymbolName}");
                Console.WriteLine($"Kind: {result.SymbolKind}");
                Console.WriteLine($"Definition: {result.DefinitionLocation}");
                Console.WriteLine($"Reference Count: {result.ReferenceCount}");
                
                if (options.Verbose && result.References.Any())
                {
                    Console.WriteLine("\nReferences:");
                    foreach (var reference in result.References)
                    {
                        Console.WriteLine($"  {reference}");
                    }
                }
            }

            return 0;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Error: {ex.Message}");
            if (options.Verbose)
            {
                Console.Error.WriteLine(ex.StackTrace);
            }
            return 1;
        }
    }
}
