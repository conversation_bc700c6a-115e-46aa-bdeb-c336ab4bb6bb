using CommandLine;
using Microsoft.Build.Locator;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.FindSymbols;
using Microsoft.CodeAnalysis.MSBuild;
using Microsoft.CodeAnalysis.Text;
using Newtonsoft.Json;
using System.Collections.Immutable;

namespace UnitySymbolAnalyzer;

public class Options
{
    [Option('s', "solution", Required = true, HelpText = "Path to Unity .sln file")]
    public string SolutionPath { get; set; } = string.Empty;

    [Option('f', "file", Required = true, HelpText = "Path to C# file (relative to solution)")]
    public string FilePath { get; set; } = string.Empty;

    [Option('l', "line", Required = true, HelpText = "Line number (1-based)")]
    public int Line { get; set; }

    [Option('c', "column", Required = true, HelpText = "Column number (1-based)")]
    public int Column { get; set; }

    [Option('v', "verbose", Required = false, HelpText = "Enable verbose output")]
    public bool Verbose { get; set; }

    [Option('o', "output", Required = false, HelpText = "Output format (json|text)", Default = "text")]
    public string OutputFormat { get; set; } = "text";
}

public class SymbolReference
{
    public string FileName { get; set; } = string.Empty;
    public int Line { get; set; }
    public int Column { get; set; }
    public string Text { get; set; } = string.Empty;
    public string Kind { get; set; } = string.Empty;
}

public class AnalysisResult
{
    public string SymbolName { get; set; } = string.Empty;
    public string SymbolKind { get; set; } = string.Empty;
    public string DefinitionLocation { get; set; } = string.Empty;
    public int ReferenceCount { get; set; }
    public List<SymbolReference> References { get; set; } = new();
    public string? Documentation { get; set; }
    public string? Namespace { get; set; }
    public string? ContainingType { get; set; }
}

class Program
{
    static async Task<int> Main(string[] args)
    {
        return await Parser.Default.ParseArguments<Options>(args)
            .MapResult(async (Options opts) => await RunAnalysis(opts),
                      errs => Task.FromResult(1));
    }

    static async Task<int> RunAnalysis(Options options)
    {
        try
        {
            if (options.Verbose)
            {
                Console.WriteLine($"Unity Symbol Analyzer based on OmniSharp-Roslyn");
                Console.WriteLine($"Analyzing solution: {options.SolutionPath}");
                Console.WriteLine($"Target location: {options.FilePath}:{options.Line}:{options.Column}");
                Console.WriteLine();
            }

            // Register MSBuild
            if (!MSBuildLocator.IsRegistered)
            {
                try
                {
                    var instances = MSBuildLocator.QueryVisualStudioInstances().ToArray();
                    if (instances.Length > 0)
                    {
                        var instance = instances.OrderByDescending(i => i.Version).First();
                        MSBuildLocator.RegisterInstance(instance);
                        if (options.Verbose)
                            Console.WriteLine($"Registered MSBuild: {instance.Name} ({instance.Version})");
                    }
                    else
                    {
                        // Try to find .NET SDK MSBuild
                        var dotnetPath = Environment.GetEnvironmentVariable("DOTNET_ROOT") ??
                                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "dotnet");

                        if (Directory.Exists(dotnetPath))
                        {
                            var sdkPath = Path.Combine(dotnetPath, "sdk");
                            if (Directory.Exists(sdkPath))
                            {
                                var latestSdk = Directory.GetDirectories(sdkPath)
                                    .Select(d => new { Path = d, Version = Path.GetFileName(d) })
                                    .OrderByDescending(x => x.Version)
                                    .FirstOrDefault();

                                if (latestSdk != null)
                                {
                                    var msbuildPath = Path.Combine(latestSdk.Path, "MSBuild.dll");
                                    if (File.Exists(msbuildPath))
                                    {
                                        MSBuildLocator.RegisterMSBuildPath(latestSdk.Path);
                                        if (options.Verbose)
                                            Console.WriteLine($"Registered .NET SDK MSBuild: {latestSdk.Version}");
                                    }
                                    else
                                    {
                                        MSBuildLocator.RegisterDefaults();
                                        if (options.Verbose)
                                            Console.WriteLine("Registered default MSBuild");
                                    }
                                }
                                else
                                {
                                    MSBuildLocator.RegisterDefaults();
                                    if (options.Verbose)
                                        Console.WriteLine("Registered default MSBuild");
                                }
                            }
                            else
                            {
                                MSBuildLocator.RegisterDefaults();
                                if (options.Verbose)
                                    Console.WriteLine("Registered default MSBuild");
                            }
                        }
                        else
                        {
                            MSBuildLocator.RegisterDefaults();
                            if (options.Verbose)
                                Console.WriteLine("Registered default MSBuild");
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (options.Verbose)
                        Console.WriteLine($"MSBuild registration warning: {ex.Message}");

                    // Fallback to default registration
                    try
                    {
                        MSBuildLocator.RegisterDefaults();
                        if (options.Verbose)
                            Console.WriteLine("Fallback: Registered default MSBuild");
                    }
                    catch (Exception fallbackEx)
                    {
                        Console.WriteLine($"Failed to register MSBuild: {fallbackEx.Message}");
                        Console.WriteLine("Please ensure .NET SDK or Visual Studio is installed.");
                        return 1;
                    }
                }
            }

            var analyzer = new UnitySymbolAnalyzer();
            var result = await analyzer.AnalyzeSymbolAsync(
                options.SolutionPath,
                options.FilePath,
                options.Line - 1, // Convert to 0-based
                options.Column - 1, // Convert to 0-based
                options.Verbose);

            if (result == null)
            {
                Console.WriteLine("No symbol found at the specified position.");
                return 1;
            }

            if (options.OutputFormat.ToLower() == "json")
            {
                var json = JsonConvert.SerializeObject(result, Formatting.Indented);
                Console.WriteLine(json);
            }
            else
            {
                Console.WriteLine($"Symbol: {result.SymbolName}");
                Console.WriteLine($"Kind: {result.SymbolKind}");
                Console.WriteLine($"Definition: {result.DefinitionLocation}");
                Console.WriteLine($"Reference Count: {result.ReferenceCount}");
                
                if (!string.IsNullOrEmpty(result.Namespace))
                    Console.WriteLine($"Namespace: {result.Namespace}");
                
                if (!string.IsNullOrEmpty(result.ContainingType))
                    Console.WriteLine($"Containing Type: {result.ContainingType}");

                if (options.Verbose && result.References.Any())
                {
                    Console.WriteLine("\nReferences:");
                    var groupedRefs = result.References.GroupBy(r => r.FileName);
                    foreach (var group in groupedRefs.OrderBy(g => g.Key))
                    {
                        Console.WriteLine($"\n  📁 {group.Key} ({group.Count()} references)");
                        foreach (var reference in group.OrderBy(r => r.Line))
                        {
                            Console.WriteLine($"     {reference.Line + 1:3}:{reference.Column + 1:2} | {reference.Text.Trim()}");
                        }
                    }
                }
            }

            return 0;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Error: {ex.Message}");
            if (options.Verbose)
            {
                Console.Error.WriteLine(ex.StackTrace);
            }
            return 1;
        }
    }
}

public class UnitySymbolAnalyzer
{
    public async Task<AnalysisResult?> AnalyzeSymbolAsync(string solutionPath, string filePath, int line, int column, bool verbose = false)
    {
        using var workspace = MSBuildWorkspace.Create();
        
        // Configure workspace to handle Unity-specific issues
        workspace.WorkspaceFailed += (sender, e) =>
        {
            if (verbose && e.Diagnostic.Kind != WorkspaceDiagnosticKind.Warning)
            {
                Console.WriteLine($"Workspace diagnostic: {e.Diagnostic.Message}");
            }
        };

        if (verbose)
            Console.WriteLine("Loading solution...");

        var solution = await workspace.OpenSolutionAsync(solutionPath);
        
        if (verbose)
            Console.WriteLine($"Loaded {solution.Projects.Count()} projects");

        // Find the document
        var document = FindDocument(solution, filePath);
        if (document == null)
        {
            throw new FileNotFoundException($"File not found in solution: {filePath}");
        }

        if (verbose)
            Console.WriteLine($"Found document: {document.Name}");

        var sourceText = await document.GetTextAsync();
        var semanticModel = await document.GetSemanticModelAsync();
        
        if (semanticModel == null)
        {
            throw new InvalidOperationException("Could not get semantic model");
        }

        // Convert line/column to position (similar to OmniSharp's approach)
        var position = sourceText.Lines.GetPosition(new LinePosition(line, column));
        
        if (verbose)
            Console.WriteLine($"Looking for symbol at position {position}...");

        // Find symbol at position (using OmniSharp's approach)
        var symbol = await SymbolFinder.FindSymbolAtPositionAsync(semanticModel, position, workspace);
        
        if (symbol == null)
        {
            return null;
        }

        if (verbose)
            Console.WriteLine($"Found symbol: {symbol.Name} ({symbol.Kind})");

        // Find definition (similar to OmniSharp's FindUsagesService)
        var definition = await SymbolFinder.FindSourceDefinitionAsync(symbol, solution);
        var targetSymbol = definition ?? symbol;

        if (verbose)
            Console.WriteLine("Finding all references...");

        // Find all references (using OmniSharp's approach)
        var usages = await SymbolFinder.FindReferencesAsync(targetSymbol, solution);
        var references = new List<SymbolReference>();

        foreach (var usage in usages)
        {
            foreach (var location in usage.Locations)
            {
                if (location.Location.IsInSource)
                {
                    var refDocument = solution.GetDocument(location.Location.SourceTree);
                    if (refDocument != null)
                    {
                        var refSourceText = await refDocument.GetTextAsync();
                        var linePosition = refSourceText.Lines.GetLinePosition(location.Location.SourceSpan.Start);
                        var lineText = refSourceText.Lines[linePosition.Line].ToString();

                        references.Add(new SymbolReference
                        {
                            FileName = GetRelativePath(refDocument.FilePath ?? refDocument.Name, solutionPath),
                            Line = linePosition.Line,
                            Column = linePosition.Character,
                            Text = lineText,
                            Kind = "Reference"
                        });
                    }
                }
            }
        }

        // Add definition locations (similar to OmniSharp's approach)
        foreach (var usage in usages)
        {
            var def = usage.Definition;
            if (def != null && !(def is IMethodSymbol method && method.AssociatedSymbol is IPropertySymbol))
            {
                foreach (var defLocation in def.Locations)
                {
                    if (defLocation.IsInSource)
                    {
                        var defDocument = solution.GetDocument(defLocation.SourceTree);
                        if (defDocument != null)
                        {
                            var defSourceText = await defDocument.GetTextAsync();
                            var linePosition = defSourceText.Lines.GetLinePosition(defLocation.SourceSpan.Start);
                            var lineText = defSourceText.Lines[linePosition.Line].ToString();

                            references.Add(new SymbolReference
                            {
                                FileName = GetRelativePath(defDocument.FilePath ?? defDocument.Name, solutionPath),
                                Line = linePosition.Line,
                                Column = linePosition.Character,
                                Text = lineText,
                                Kind = "Definition"
                            });
                        }
                    }
                }
            }
        }

        var definitionLocation = GetSymbolDefinitionLocation(symbol);

        return new AnalysisResult
        {
            SymbolName = symbol.Name,
            SymbolKind = symbol.Kind.ToString(),
            DefinitionLocation = definitionLocation,
            ReferenceCount = references.Count,
            References = references.OrderBy(r => r.FileName).ThenBy(r => r.Line).ThenBy(r => r.Column).ToList(),
            Documentation = symbol.GetDocumentationCommentXml(),
            Namespace = symbol.ContainingNamespace?.ToDisplayString(),
            ContainingType = symbol.ContainingType?.ToDisplayString()
        };
    }

    private Document? FindDocument(Solution solution, string filePath)
    {
        // Try exact match first
        var document = solution.Projects
            .SelectMany(p => p.Documents)
            .FirstOrDefault(d => string.Equals(d.FilePath, filePath, StringComparison.OrdinalIgnoreCase));

        if (document != null) return document;

        // Try relative path matching
        var fileName = Path.GetFileName(filePath);
        return solution.Projects
            .SelectMany(p => p.Documents)
            .FirstOrDefault(d => d.Name.Equals(fileName, StringComparison.OrdinalIgnoreCase) ||
                               d.FilePath?.EndsWith(filePath.Replace('\\', '/'), StringComparison.OrdinalIgnoreCase) == true);
    }

    private string GetRelativePath(string fullPath, string solutionPath)
    {
        var solutionDir = Path.GetDirectoryName(solutionPath) ?? "";
        try
        {
            return Path.GetRelativePath(solutionDir, fullPath);
        }
        catch
        {
            return fullPath;
        }
    }

    private string GetSymbolDefinitionLocation(ISymbol symbol)
    {
        var location = symbol.Locations.FirstOrDefault(l => l.IsInSource);
        if (location != null)
        {
            var linePosition = location.GetLineSpan().StartLinePosition;
            return $"{location.SourceTree?.FilePath}:{linePosition.Line + 1}:{linePosition.Character + 1}";
        }

        return symbol.ToDisplayString();
    }
}
