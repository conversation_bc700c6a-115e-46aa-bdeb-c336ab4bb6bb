using System.Text.RegularExpressions;

namespace UnitySymbolAnalyzer.Core;

public class SolutionParser
{
    private static readonly Regex ProjectRegex = new(
        @"Project\(""\{[^}]+\}""\)\s*=\s*""([^""]+)""\s*,\s*""([^""]+)""\s*,\s*""\{[^}]+\}""",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);

    public class ProjectInfo
    {
        public string Name { get; set; } = string.Empty;
        public string RelativePath { get; set; } = string.Empty;
        public string FullPath { get; set; } = string.Empty;
        public bool IsCSharpProject => RelativePath.EndsWith(".csproj", StringComparison.OrdinalIgnoreCase);
    }

    public async Task<List<ProjectInfo>> ParseSolutionAsync(string solutionPath)
    {
        if (!File.Exists(solutionPath))
        {
            throw new FileNotFoundException($"Solution file not found: {solutionPath}");
        }

        var solutionDirectory = Path.GetDirectoryName(solutionPath) ?? string.Empty;
        var projects = new List<ProjectInfo>();

        var content = await File.ReadAllTextAsync(solutionPath);
        var matches = ProjectRegex.Matches(content);

        foreach (Match match in matches)
        {
            var projectName = match.Groups[1].Value;
            var projectRelativePath = match.Groups[2].Value;

            // Skip solution folders and non-C# projects
            if (!projectRelativePath.EndsWith(".csproj", StringComparison.OrdinalIgnoreCase))
                continue;

            var projectFullPath = Path.GetFullPath(Path.Combine(solutionDirectory, projectRelativePath));

            projects.Add(new ProjectInfo
            {
                Name = projectName,
                RelativePath = projectRelativePath,
                FullPath = projectFullPath
            });
        }

        return projects;
    }

    public async Task<bool> ValidateSolutionAsync(string solutionPath)
    {
        try
        {
            if (!File.Exists(solutionPath))
                return false;

            var content = await File.ReadAllTextAsync(solutionPath);
            return content.Contains("Microsoft Visual Studio Solution File");
        }
        catch
        {
            return false;
        }
    }
}
