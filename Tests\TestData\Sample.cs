using System;
using UnityEngine;

namespace TestProject
{
    public class PlayerController : MonoBehaviour
    {
        [SerializeField]
        private float speed = 5.0f;
        
        private Rigidbody rb;
        private Vector3 movement;

        void Start()
        {
            rb = GetComponent<Rigidbody>();
        }

        void Update()
        {
            HandleInput();
            MovePlayer();
        }

        private void HandleInput()
        {
            float horizontal = Input.GetAxis("Horizontal");
            float vertical = Input.GetAxis("Vertical");
            
            movement = new Vector3(horizontal, 0, vertical);
        }

        private void MovePlayer()
        {
            if (movement.magnitude > 0.1f)
            {
                rb.velocity = movement * speed;
            }
        }

        public void SetSpeed(float newSpeed)
        {
            speed = newSpeed;
        }

        public float GetSpeed()
        {
            return speed;
        }
    }

    public class GameManager : MonoBehaviour
    {
        public PlayerController player;
        
        void Start()
        {
            if (player != null)
            {
                player.SetSpeed(10.0f);
                Debug.Log($"Player speed: {player.GetSpeed()}");
            }
        }
    }
}
