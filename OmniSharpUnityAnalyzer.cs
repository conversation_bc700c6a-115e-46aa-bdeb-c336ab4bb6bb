using CommandLine;
using OmniSharp.LanguageServerProtocol;
using OmniSharp.Models.FindUsages;
using OmniSharp.Roslyn.CSharp.Services.Navigation;
using OmniSharp;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using OmniSharp.Services;
using OmniSharp.Options;
using Microsoft.Extensions.Options;
using OmniSharp.Stdio.Eventing;
using OmniSharp.Stdio.Logging;
using Newtonsoft.Json;
using System.Threading;

namespace UnitySymbolAnalyzer;

public class UnityAnalyzerOptions
{
    [Option('s', "solution", Required = false, HelpText = "Path to Unity .sln file")]
    public string? SolutionPath { get; set; }

    [Option('f', "file", Required = false, HelpText = "Path to C# file (relative to solution)")]
    public string? FilePath { get; set; }

    [Option('l', "line", Required = false, HelpText = "Line number (1-based)")]
    public int Line { get; set; }

    [Option('c', "column", Required = false, HelpText = "Column number (1-based)")]
    public int Column { get; set; }

    [Option('v', "verbose", Required = false, HelpText = "Enable verbose output")]
    public bool Verbose { get; set; }

    [Option('o', "output", Required = false, HelpText = "Output format (json|text)", Default = "text")]
    public string OutputFormat { get; set; } = "text";

    [Option("lsp", Required = false, HelpText = "Start as LSP server")]
    public bool Lsp { get; set; }

    [Option("source", Required = false, HelpText = "Source directory")]
    public string? Source { get; set; }

    [Option("encoding", Required = false, HelpText = "Input / output encoding")]
    public string? Encoding { get; set; }

    [Option("zero-based-indices", Required = false, HelpText = "Use zero based indices")]
    public bool ZeroBasedIndices { get; set; }

    [Option("log-level", Required = false, HelpText = "Log level", Default = LogLevel.Information)]
    public LogLevel LogLevel { get; set; } = LogLevel.Information;
}

class OmniSharpUnityAnalyzer
{
    static int Main(string[] args) => HostHelpers.Start(() =>
    {
        return Parser.Default.ParseArguments<UnityAnalyzerOptions>(args)
            .MapResult(async (UnityAnalyzerOptions opts) => await RunAnalysis(opts),
                      errs => Task.FromResult(1)).Result;
    });

    static async Task<int> RunAnalysis(UnityAnalyzerOptions options)
    {
        try
        {
            if (options.Lsp)
            {
                // Start LSP server mode
                return await StartLspServer(options);
            }
            else if (!string.IsNullOrEmpty(options.SolutionPath) && 
                     !string.IsNullOrEmpty(options.FilePath) && 
                     options.Line > 0 && options.Column > 0)
            {
                // Direct analysis mode
                return await AnalyzeSymbol(options);
            }
            else
            {
                Console.WriteLine("Usage:");
                Console.WriteLine("  LSP Server mode: --lsp --source <path>");
                Console.WriteLine("  Direct analysis: -s <solution> -f <file> -l <line> -c <column>");
                return 1;
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Error: {ex.Message}");
            if (options.Verbose)
            {
                Console.Error.WriteLine(ex.StackTrace);
            }
            return 1;
        }
    }

    static async Task<int> StartLspServer(UnityAnalyzerOptions options)
    {
        if (options.Verbose)
        {
            Console.WriteLine("Starting OmniSharp-based Unity C# Language Server...");
        }

        // Set encoding if specified
        if (options.Encoding != null)
        {
            var encoding = Encoding.GetEncoding(options.Encoding);
            Console.InputEncoding = encoding;
            Console.OutputEncoding = encoding;
        }

        var cancellation = new CancellationTokenSource();
        
        // Configure for LSP mode
        Configuration.ZeroBasedIndices = true;

        // Create a custom command line application for Unity
        var application = new UnityCommandLineApplication(options);

        using (var host = new LanguageServerHost(
            Console.OpenStandardInput(),
            Console.OpenStandardOutput(),
            application,
            cancellation))
        {
            if (options.Verbose)
            {
                Console.WriteLine("LSP Server started, waiting for client connection...");
            }

            await host.Start();
            cancellation.Token.WaitHandle.WaitOne();
        }

        return 0;
    }

    static async Task<int> AnalyzeSymbol(UnityAnalyzerOptions options)
    {
        if (options.Verbose)
        {
            Console.WriteLine($"Unity Symbol Analyzer based on OmniSharp-Roslyn");
            Console.WriteLine($"Analyzing solution: {options.SolutionPath}");
            Console.WriteLine($"Target location: {options.FilePath}:{options.Line}:{options.Column}");
            Console.WriteLine();
        }

        // Create OmniSharp environment
        var environment = new OmniSharpEnvironment(
            path: options.Source ?? Path.GetDirectoryName(options.SolutionPath) ?? Environment.CurrentDirectory,
            logLevel: options.LogLevel);

        var configurationResult = new ConfigurationBuilder(environment).Build();
        var writer = new SharedTextWriter(Console.Out);
        
        var serviceProvider = CompositionHostBuilder.CreateDefaultServiceProvider(
            environment, 
            configurationResult.Configuration, 
            new StdioEventEmitter(writer),
            configureLogging: builder => builder.AddStdio(writer));

        var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
        var assemblyLoader = serviceProvider.GetRequiredService<IAssemblyLoader>();
        var optionsMonitor = serviceProvider.GetRequiredService<IOptionsMonitor<OmniSharpOptions>>();
        
        var logger = loggerFactory.CreateLogger<OmniSharpUnityAnalyzer>();
        
        if (configurationResult.HasError())
        {
            logger.LogError(configurationResult.Exception, "Configuration error, using defaults");
        }

        var compositionHostBuilder = new CompositionHostBuilder(serviceProvider)
            .WithOmniSharpAssemblies();

        var cancellation = new CancellationTokenSource();

        using (var host = new Host(
            Console.In, 
            writer, 
            environment, 
            serviceProvider, 
            compositionHostBuilder, 
            loggerFactory, 
            cancellation))
        {
            host.Start();

            // Wait for initialization
            await Task.Delay(2000);

            // Create FindUsages request
            var request = new FindUsagesRequest
            {
                FileName = Path.Combine(environment.TargetDirectory, options.FilePath!),
                Line = options.Line - 1, // Convert to 0-based
                Column = options.Column - 1, // Convert to 0-based
                OnlyThisFile = false,
                ExcludeDefinition = false
            };

            if (options.Verbose)
            {
                Console.WriteLine($"Looking for symbol at {request.FileName}:{request.Line}:{request.Column}");
            }

            // Get FindUsages service
            var findUsagesService = host.GetExport<FindUsagesService>();
            if (findUsagesService == null)
            {
                Console.WriteLine("FindUsagesService not available");
                return 1;
            }

            // Execute the request
            var response = await findUsagesService.Handle(request);

            if (response?.QuickFixes == null || !response.QuickFixes.Any())
            {
                Console.WriteLine("No symbol found at the specified position.");
                return 1;
            }

            // Output results
            if (options.OutputFormat.ToLower() == "json")
            {
                var json = JsonConvert.SerializeObject(response, Formatting.Indented);
                Console.WriteLine(json);
            }
            else
            {
                Console.WriteLine($"Found {response.QuickFixes.Count()} references:");
                Console.WriteLine();

                var groupedRefs = response.QuickFixes.GroupBy(qf => qf.FileName);
                foreach (var group in groupedRefs.OrderBy(g => g.Key))
                {
                    var relativePath = Path.GetRelativePath(environment.TargetDirectory, group.Key);
                    Console.WriteLine($"📁 {relativePath} ({group.Count()} references)");
                    
                    foreach (var quickFix in group.OrderBy(qf => qf.Line).ThenBy(qf => qf.Column))
                    {
                        Console.WriteLine($"   {quickFix.Line + 1:3}:{quickFix.Column + 1:2} | {quickFix.Text.Trim()}");
                    }
                    Console.WriteLine();
                }

                Console.WriteLine($"Total references: {response.QuickFixes.Count()}");
            }

            cancellation.Cancel();
        }

        return 0;
    }
}

// Custom command line application for Unity projects
public class UnityCommandLineApplication : CommandLineApplication
{
    private readonly UnityAnalyzerOptions _options;

    public UnityCommandLineApplication(UnityAnalyzerOptions options)
    {
        _options = options;
    }

    public override OmniSharpEnvironment CreateEnvironment()
    {
        return new OmniSharpEnvironment(
            path: _options.Source ?? Environment.CurrentDirectory,
            logLevel: _options.LogLevel);
    }

    public override bool ZeroBasedIndices => _options.ZeroBasedIndices;
    public override LogLevel LogLevel => _options.LogLevel;
    public override bool Lsp => _options.Lsp;
    public override string? Encoding => _options.Encoding;
}
