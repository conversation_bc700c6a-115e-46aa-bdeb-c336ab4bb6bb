# Unity Symbol Analyzer Demo Script

Write-Host "Unity Symbol Analyzer Demo" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

Write-Host "`nThis tool analyzes Unity C# projects to find symbol references." -ForegroundColor White
Write-Host "It can help you understand code dependencies and usage patterns." -ForegroundColor White

Write-Host "`n1. First, make sure your Unity project has a .sln file:" -ForegroundColor Yellow
Write-Host "   - Open your Unity project" -ForegroundColor White
Write-Host "   - Go to Assets -> Open C# Project" -ForegroundColor White
Write-Host "   - This will generate the .sln file needed for analysis" -ForegroundColor White

Write-Host "`n2. Basic usage example:" -ForegroundColor Yellow
Write-Host "   dotnet run -- -s ""YourProject.sln"" -f ""Assets/Scripts/PlayerController.cs"" -l 10 -c 15" -ForegroundColor Cyan

Write-Host "`n3. Command line options:" -ForegroundColor Yellow
Write-Host "   -s, --solution : Path to Unity .sln file (required)" -ForegroundColor White
Write-Host "   -f, --file     : Path to C# file relative to solution (required)" -ForegroundColor White
Write-Host "   -l, --line     : Line number (1-based, required)" -ForegroundColor White
Write-Host "   -c, --column   : Column number (1-based, required)" -ForegroundColor White
Write-Host "   -v, --verbose  : Enable verbose output (optional)" -ForegroundColor White
Write-Host "   -o, --output   : Output format - 'text' or 'json' (optional)" -ForegroundColor White

Write-Host "`n4. Example scenarios:" -ForegroundColor Yellow

Write-Host "`n   Scenario A: Find all references to a method" -ForegroundColor Cyan
Write-Host "   - Position your cursor on a method name in your IDE" -ForegroundColor White
Write-Host "   - Note the line and column numbers" -ForegroundColor White
Write-Host "   - Run: dotnet run -- -s ""Game.sln"" -f ""Scripts/Player.cs"" -l 25 -c 10" -ForegroundColor Gray

Write-Host "`n   Scenario B: Analyze a variable usage" -ForegroundColor Cyan
Write-Host "   - Click on a variable or field name" -ForegroundColor White
Write-Host "   - Run: dotnet run -- -s ""Game.sln"" -f ""Scripts/GameManager.cs"" -l 15 -c 20 -v" -ForegroundColor Gray

Write-Host "`n   Scenario C: Get JSON output for automation" -ForegroundColor Cyan
Write-Host "   - Run: dotnet run -- -s ""Game.sln"" -f ""Scripts/Enemy.cs"" -l 30 -c 12 -o json" -ForegroundColor Gray

Write-Host "`n5. What the tool analyzes:" -ForegroundColor Yellow
Write-Host "   ✓ Classes, interfaces, structs" -ForegroundColor Green
Write-Host "   ✓ Methods, properties, fields" -ForegroundColor Green
Write-Host "   ✓ Local variables and parameters" -ForegroundColor Green
Write-Host "   ✓ Namespaces and using directives" -ForegroundColor Green
Write-Host "   ✓ Generic types and methods" -ForegroundColor Green

Write-Host "`n6. Sample output:" -ForegroundColor Yellow
Write-Host @"
   Symbol: Jump
   Kind: Method
   Definition: Assets/Scripts/PlayerController.cs:25:5
   Reference Count: 3
   
   References:
     Assets/Scripts/PlayerController.cs:25:5 - public void Jump()
     Assets/Scripts/InputHandler.cs:15:20 - player.Jump();
     Assets/Scripts/AIController.cs:30:15 - controller.Jump();
"@ -ForegroundColor Gray

Write-Host "`n7. Tips for best results:" -ForegroundColor Yellow
Write-Host "   • Use exact line/column positions from your IDE" -ForegroundColor White
Write-Host "   • File paths should be relative to the solution directory" -ForegroundColor White
Write-Host "   • Use verbose mode (-v) when troubleshooting" -ForegroundColor White
Write-Host "   • Large projects may take a few seconds to analyze" -ForegroundColor White

Write-Host "`nReady to analyze your Unity project!" -ForegroundColor Green
Write-Host "Run 'dotnet run -- --help' for detailed command options." -ForegroundColor Cyan
