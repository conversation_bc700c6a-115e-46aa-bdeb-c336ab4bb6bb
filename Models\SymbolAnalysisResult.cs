namespace UnitySymbolAnalyzer.Models;

public class SymbolAnalysisResult
{
    public string SymbolName { get; set; } = string.Empty;
    public string SymbolKind { get; set; } = string.Empty;
    public string DefinitionLocation { get; set; } = string.Empty;
    public int ReferenceCount { get; set; }
    public List<string> References { get; set; } = new();
    public string? Documentation { get; set; }
    public string? Namespace { get; set; }
    public string? ContainingType { get; set; }
}

public class ReferenceLocation
{
    public string FilePath { get; set; } = string.Empty;
    public int Line { get; set; }
    public int Column { get; set; }
    public string Context { get; set; } = string.Empty;

    public override string ToString()
    {
        return $"{FilePath}:{Line + 1}:{Column + 1} - {Context}";
    }
}
