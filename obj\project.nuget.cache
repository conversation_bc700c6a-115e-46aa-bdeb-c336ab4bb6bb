{"version": 2, "dgSpecHash": "jNke6/BJAwc=", "success": false, "projectFilePath": "E:\\workspace\\cs-language-server\\UnitySymbolAnalyzer.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\commandlineparser\\2.9.1\\commandlineparser.2.9.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnet.script.dependencymodel\\1.5.0\\dotnet.script.dependencymodel.1.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnet.script.dependencymodel.nuget\\1.5.0\\dotnet.script.dependencymodel.nuget.1.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\icsharpcode.decompiler\\9.1.0.7988\\icsharpcode.decompiler.9.1.0.7988.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mcmaster.extensions.commandlineutils\\4.1.0\\mcmaster.extensions.commandlineutils.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr\\8.1.0\\mediatr.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\9.0.0\\microsoft.bcl.asyncinterfaces.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.locator\\1.6.10\\microsoft.build.locator.1.6.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.11.0\\microsoft.codeanalysis.analyzers.3.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\5.0.0-2.25404.8\\microsoft.codeanalysis.common.5.0.0-2.25404.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\5.0.0-2.25404.8\\microsoft.codeanalysis.csharp.5.0.0-2.25404.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp.features\\5.0.0-2.25404.8\\microsoft.codeanalysis.csharp.features.5.0.0-2.25404.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp.workspaces\\5.0.0-2.25404.8\\microsoft.codeanalysis.csharp.workspaces.5.0.0-2.25404.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.elfie\\1.0.0\\microsoft.codeanalysis.elfie.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.externalaccess.aspnetcore\\5.0.0-2.25404.8\\microsoft.codeanalysis.externalaccess.aspnetcore.5.0.0-2.25404.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.externalaccess.omnisharp\\5.0.0-2.25404.8\\microsoft.codeanalysis.externalaccess.omnisharp.5.0.0-2.25404.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.externalaccess.omnisharp.csharp\\5.0.0-2.25404.8\\microsoft.codeanalysis.externalaccess.omnisharp.csharp.5.0.0-2.25404.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.features\\5.0.0-2.25404.8\\microsoft.codeanalysis.features.5.0.0-2.25404.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.scripting.common\\5.0.0-2.25404.8\\microsoft.codeanalysis.scripting.common.5.0.0-2.25404.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.workspaces.common\\5.0.0-2.25404.8\\microsoft.codeanalysis.workspaces.common.5.0.0-2.25404.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.diasymreader\\2.0.0\\microsoft.diasymreader.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.dotnet.platformabstractions\\3.1.6\\microsoft.dotnet.platformabstractions.3.1.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\9.0.0\\microsoft.extensions.caching.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\9.0.0\\microsoft.extensions.caching.memory.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.0\\microsoft.extensions.configuration.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.0\\microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.0\\microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\9.0.0\\microsoft.extensions.configuration.commandline.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\9.0.0\\microsoft.extensions.configuration.environmentvariables.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\9.0.0\\microsoft.extensions.configuration.fileextensions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\9.0.0\\microsoft.extensions.configuration.json.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.0\\microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.0\\microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\9.0.0\\microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.0\\microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\9.0.0\\microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\9.0.0\\microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.0\\microsoft.extensions.logging.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.0\\microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\9.0.0\\microsoft.extensions.logging.configuration.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.console\\9.0.0\\microsoft.extensions.logging.console.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.0\\microsoft.extensions.options.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\9.0.0\\microsoft.extensions.options.configurationextensions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.0\\microsoft.extensions.primitives.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.testplatform.objectmodel\\17.14.0-preview-25107-01\\microsoft.testplatform.objectmodel.17.14.0-preview-25107-01.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.testplatform.translationlayer\\17.14.0-preview-25107-01\\microsoft.testplatform.translationlayer.17.14.0-preview-25107-01.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.threading\\17.6.40\\microsoft.visualstudio.threading.17.6.40.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.threading.analyzers\\17.6.40\\microsoft.visualstudio.threading.analyzers.17.6.40.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.validation\\17.6.11\\microsoft.visualstudio.validation.17.6.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nerdbank.streams\\2.10.69\\nerdbank.streams.2.10.69.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\2.0.0\\netstandard.library.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.common\\6.15.0-preview.1.86\\nuget.common.6.15.0-preview.1.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.configuration\\6.15.0-preview.1.86\\nuget.configuration.6.15.0-preview.1.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.dependencyresolver.core\\6.15.0-preview.1.86\\nuget.dependencyresolver.core.6.15.0-preview.1.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.frameworks\\6.15.0-preview.1.86\\nuget.frameworks.6.15.0-preview.1.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.librarymodel\\6.15.0-preview.1.86\\nuget.librarymodel.6.15.0-preview.1.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.packaging\\6.15.0-preview.1.86\\nuget.packaging.6.15.0-preview.1.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.projectmodel\\6.15.0-preview.1.86\\nuget.projectmodel.6.15.0-preview.1.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.protocol\\6.15.0-preview.1.86\\nuget.protocol.6.15.0-preview.1.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.versioning\\6.15.0-preview.1.86\\nuget.versioning.6.15.0-preview.1.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\omnisharp.extensions.jsonrpc\\0.19.9\\omnisharp.extensions.jsonrpc.0.19.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\omnisharp.extensions.jsonrpc.generators\\0.19.9\\omnisharp.extensions.jsonrpc.generators.0.19.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\omnisharp.extensions.languageprotocol\\0.19.9\\omnisharp.extensions.languageprotocol.0.19.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\omnisharp.extensions.languageserver\\0.19.9\\omnisharp.extensions.languageserver.0.19.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\omnisharp.extensions.languageserver.shared\\0.19.9\\omnisharp.extensions.languageserver.shared.0.19.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.6.0\\system.buffers.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\9.0.0\\system.collections.immutable.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.composition\\9.0.0\\system.componentmodel.composition.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition\\9.0.0\\system.composition.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.attributedmodel\\9.0.0\\system.composition.attributedmodel.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.convention\\9.0.0\\system.composition.convention.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.hosting\\9.0.0\\system.composition.hosting.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.runtime\\9.0.0\\system.composition.runtime.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.typedparts\\9.0.0\\system.composition.typedparts.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\9.0.0\\system.configuration.configurationmanager.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\9.0.0\\system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\9.0.0\\system.diagnostics.eventlog.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\9.0.0\\system.io.pipelines.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.6.0\\system.memory.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.6.0\\system.numerics.vectors.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reactive\\6.0.1\\system.reactive.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\9.0.0\\system.reflection.metadata.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.1.0\\system.runtime.compilerservices.unsafe.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\6.0.4\\system.security.cryptography.pkcs.6.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\9.0.0\\system.security.cryptography.protecteddata.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\9.0.0\\system.text.encodings.web.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.0\\system.text.json.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.dataflow\\9.0.0\\system.threading.tasks.dataflow.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.6.0\\system.threading.tasks.extensions.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.ref\\8.0.18\\microsoft.netcore.app.ref.8.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.ref\\8.0.18\\microsoft.windowsdesktop.app.ref.8.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.ref\\8.0.18\\microsoft.aspnetcore.app.ref.8.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.host.win-x64\\8.0.18\\microsoft.netcore.app.host.win-x64.8.0.18.nupkg.sha512"], "logs": [{"code": "NU1102", "level": "Error", "message": "找不到版本为 (>= 5.0.0-2.25404.8) 的包 Microsoft.CodeAnalysis.CSharp.Scripting\r\n  - 在 NuGet 中找到 150 个版本[ 最接近版本: 4.14.0 ]\r\n  - 在 Microsoft Visual Studio Offline Packages 中找到 0 个版本", "projectPath": "E:\\workspace\\cs-language-server\\UnitySymbolAnalyzer.csproj", "filePath": "E:\\workspace\\cs-language-server\\UnitySymbolAnalyzer.csproj", "libraryId": "Microsoft.CodeAnalysis.CSharp.Scripting", "targetGraphs": ["net8.0"]}]}