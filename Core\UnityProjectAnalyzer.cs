using Microsoft.Build.Locator;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.FindSymbols;
using Microsoft.CodeAnalysis.MSBuild;
using Microsoft.CodeAnalysis.Text;
using UnitySymbolAnalyzer.Models;

namespace UnitySymbolAnalyzer.Core;

public class UnityProjectAnalyzer
{
    private Solution? _solution;
    private MSBuildWorkspace? _workspace;
    private readonly SolutionParser _solutionParser;

    public UnityProjectAnalyzer()
    {
        _solutionParser = new SolutionParser();
        
        // Register MSBuild if not already registered
        if (!MSBuildLocator.IsRegistered)
        {
            MSBuildLocator.RegisterDefaults();
        }
    }

    public async Task LoadSolutionAsync(string solutionPath)
    {
        if (!await _solutionParser.ValidateSolutionAsync(solutionPath))
        {
            throw new ArgumentException($"Invalid solution file: {solutionPath}");
        }

        _workspace = MSBuildWorkspace.Create();
        
        // Configure workspace to handle Unity-specific issues
        _workspace.WorkspaceFailed += (sender, e) =>
        {
            if (e.Diagnostic.Kind == WorkspaceDiagnosticKind.Warning)
            {
                // Ignore warnings for Unity projects
                return;
            }
            Console.WriteLine($"Workspace error: {e.Diagnostic.Message}");
        };

        try
        {
            _solution = await _workspace.OpenSolutionAsync(solutionPath);
            Console.WriteLine($"Loaded solution with {_solution.Projects.Count()} projects");
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to load solution: {ex.Message}", ex);
        }
    }

    public async Task<SymbolAnalysisResult?> AnalyzeSymbolAtPositionAsync(string filePath, int line, int column)
    {
        if (_solution == null)
        {
            throw new InvalidOperationException("Solution not loaded. Call LoadSolutionAsync first.");
        }

        // Find the document
        var document = FindDocument(filePath);
        if (document == null)
        {
            throw new FileNotFoundException($"File not found in solution: {filePath}");
        }

        var sourceText = await document.GetTextAsync();
        var position = sourceText.Lines[line].Start + column;

        // Get syntax tree and semantic model
        var syntaxTree = await document.GetSyntaxTreeAsync();
        var semanticModel = await document.GetSemanticModelAsync();

        if (syntaxTree == null || semanticModel == null)
        {
            return null;
        }

        // Find symbol at position
        var symbol = await FindSymbolAtPositionAsync(syntaxTree, semanticModel, position);
        if (symbol == null)
        {
            return null;
        }

        // Find all references
        var references = await FindAllReferencesAsync(symbol);

        return new SymbolAnalysisResult
        {
            SymbolName = symbol.Name,
            SymbolKind = symbol.Kind.ToString(),
            DefinitionLocation = GetSymbolLocation(symbol),
            ReferenceCount = references.Count,
            References = references.Select(r => r.ToString()).ToList(),
            Documentation = symbol.GetDocumentationCommentXml(),
            Namespace = symbol.ContainingNamespace?.ToDisplayString(),
            ContainingType = symbol.ContainingType?.ToDisplayString()
        };
    }

    private Document? FindDocument(string filePath)
    {
        if (_solution == null) return null;

        // Try exact match first
        var document = _solution.Projects
            .SelectMany(p => p.Documents)
            .FirstOrDefault(d => string.Equals(d.FilePath, filePath, StringComparison.OrdinalIgnoreCase));

        if (document != null) return document;

        // Try relative path matching
        var fileName = Path.GetFileName(filePath);
        return _solution.Projects
            .SelectMany(p => p.Documents)
            .FirstOrDefault(d => d.Name.Equals(fileName, StringComparison.OrdinalIgnoreCase) ||
                               d.FilePath?.EndsWith(filePath.Replace('\\', '/'), StringComparison.OrdinalIgnoreCase) == true);
    }

    private async Task<ISymbol?> FindSymbolAtPositionAsync(SyntaxTree syntaxTree, SemanticModel semanticModel, int position)
    {
        var root = await syntaxTree.GetRootAsync();
        var node = root.FindToken(position).Parent;

        while (node != null)
        {
            var symbol = semanticModel.GetSymbolInfo(node).Symbol;
            if (symbol != null)
            {
                return symbol;
            }

            // Try declared symbol for definitions
            symbol = semanticModel.GetDeclaredSymbol(node);
            if (symbol != null)
            {
                return symbol;
            }

            node = node.Parent;
        }

        return null;
    }

    private async Task<List<Models.ReferenceLocation>> FindAllReferencesAsync(ISymbol symbol)
    {
        if (_solution == null) return new List<Models.ReferenceLocation>();

        var references = new List<Models.ReferenceLocation>();
        var referencedSymbols = await SymbolFinder.FindReferencesAsync(symbol, _solution);

        foreach (var referencedSymbol in referencedSymbols)
        {
            foreach (var location in referencedSymbol.Locations)
            {
                if (location.Location.IsInSource)
                {
                    var document = _solution.GetDocument(location.Location.SourceTree);
                    if (document != null)
                    {
                        var sourceText = await document.GetTextAsync();
                        var linePosition = sourceText.Lines.GetLinePosition(location.Location.SourceSpan.Start);
                        var line = sourceText.Lines[linePosition.Line];
                        
                        references.Add(new Models.ReferenceLocation
                        {
                            FilePath = document.FilePath ?? document.Name,
                            Line = linePosition.Line,
                            Column = linePosition.Character,
                            Context = line.ToString().Trim()
                        });
                    }
                }
            }
        }

        return references;
    }

    private string GetSymbolLocation(ISymbol symbol)
    {
        var location = symbol.Locations.FirstOrDefault(l => l.IsInSource);
        if (location != null)
        {
            var linePosition = location.GetLineSpan().StartLinePosition;
            return $"{location.SourceTree?.FilePath}:{linePosition.Line + 1}:{linePosition.Character + 1}";
        }

        return symbol.ToDisplayString();
    }

    public void Dispose()
    {
        _workspace?.Dispose();
    }
}
