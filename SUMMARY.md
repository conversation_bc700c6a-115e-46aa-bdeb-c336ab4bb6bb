# Unity Symbol Analyzer - 项目总结

## 项目概述

Unity Symbol Analyzer 是一个基于 C# Language Server 的离线代码分析工具，专门用于分析Unity项目。它可以加载Unity的.sln文件，定位到C#文件中的特定位置（行号、列号），找到对应的symbol，并统计该symbol在整个项目中的引用数量。

## 核心功能

### 1. 离线分析
- 无需启动Unity编辑器
- 直接解析.sln文件和C#项目文件
- 使用Microsoft Roslyn进行代码分析

### 2. 精确定位
- 根据文件路径、行号、列号精确定位symbol
- 支持各种C#语言元素（类、方法、字段、变量等）
- 智能识别symbol类型和上下文

### 3. 引用统计
- 统计symbol在整个解决方案中的引用次数
- 显示每个引用的具体位置和上下文
- 支持跨项目引用分析

### 4. 多种输出格式
- 人类可读的文本格式
- 结构化的JSON格式（便于集成到其他工具）
- 详细的调试信息（verbose模式）

## 技术架构

### 核心组件

1. **SolutionParser** (`Core/SolutionParser.cs`)
   - 解析Unity的.sln文件
   - 提取项目信息和文件路径
   - 验证解决方案文件有效性

2. **UnityProjectAnalyzer** (`Core/UnityProjectAnalyzer.cs`)
   - 使用MSBuild Workspace加载项目
   - 构建语法树和语义模型
   - 执行symbol定位和引用查找

3. **SymbolAnalysisResult** (`Models/SymbolAnalysisResult.cs`)
   - 定义分析结果的数据结构
   - 包含symbol信息、引用位置等

4. **Program** (`Program.cs`)
   - 命令行接口实现
   - 参数解析和结果输出

### 技术栈

- **.NET 8.0**: 现代化的.NET平台
- **Microsoft.CodeAnalysis (Roslyn)**: C#代码分析引擎
- **Microsoft.Build**: MSBuild项目加载
- **CommandLineParser**: 命令行参数解析
- **Newtonsoft.Json**: JSON序列化

## 使用方法

### 基本命令
```bash
dotnet run -- -s "Unity项目.sln" -f "Assets/Scripts/脚本.cs" -l 行号 -c 列号
```

### 命令行参数
- `-s, --solution`: Unity .sln文件路径（必需）
- `-f, --file`: C#文件路径，相对于解决方案目录（必需）
- `-l, --line`: 行号，从1开始（必需）
- `-c, --column`: 列号，从1开始（必需）
- `-v, --verbose`: 启用详细输出（可选）
- `-o, --output`: 输出格式，text或json（可选，默认text）

### 示例用法

1. **分析方法引用**
   ```bash
   dotnet run -- -s "MyGame.sln" -f "Assets/Scripts/Player.cs" -l 25 -c 10
   ```

2. **获取JSON输出**
   ```bash
   dotnet run -- -s "MyGame.sln" -f "Scripts/GameManager.cs" -l 15 -c 20 -o json
   ```

3. **详细模式分析**
   ```bash
   dotnet run -- -s "MyGame.sln" -f "Scripts/Enemy.cs" -l 30 -c 5 -v
   ```

## 支持的Symbol类型

- 类、接口、结构体
- 方法、属性、字段
- 局部变量和参数
- 命名空间和using指令
- 泛型类型和方法
- Unity特定组件和属性

## 项目结构

```
UnitySymbolAnalyzer/
├── Core/                          # 核心分析组件
│   ├── SolutionParser.cs         # 解决方案文件解析器
│   └── UnityProjectAnalyzer.cs   # 主要分析引擎
├── Models/                        # 数据模型
│   └── SymbolAnalysisResult.cs   # 分析结果模型
├── Examples/                      # 使用示例
│   └── usage_examples.md         # 详细使用示例
├── Scripts/                       # 构建脚本
│   └── build.ps1                 # PowerShell构建脚本
├── Tests/                         # 测试数据
│   └── TestData/                  # 示例Unity项目文件
├── Program.cs                     # 主程序入口
├── UnitySymbolAnalyzer.csproj    # 项目文件
├── README.md                      # 项目说明
├── demo.ps1                       # 演示脚本
└── test_basic.ps1                # 基础测试脚本
```

## 优势特点

1. **离线工作**: 不依赖Unity编辑器，可以在CI/CD环境中使用
2. **高精度**: 基于Roslyn的语义分析，准确识别symbol引用
3. **跨项目**: 支持多项目解决方案的引用分析
4. **易集成**: 提供JSON输出，便于集成到其他工具链
5. **高性能**: 利用Roslyn的增量编译和缓存机制

## 应用场景

1. **代码重构**: 在重命名或删除代码前了解影响范围
2. **代码审查**: 理解代码变更的影响
3. **文档生成**: 为API生成使用报告
4. **调试分析**: 追踪变量或方法的使用位置
5. **代码质量**: 识别未使用或过度使用的symbol

## 限制说明

- 需要Unity生成的.sln文件（通过"Open C# Project"生成）
- 仅基于源代码分析，不包含运行时分析
- 某些Unity特定属性可能无法完全解析
- 分析大型项目时可能需要较长时间

## 未来扩展

- 支持更多输出格式（XML、CSV等）
- 添加批量分析功能
- 集成代码度量分析
- 支持增量分析和缓存
- 提供Web API接口

这个工具为Unity开发者提供了强大的离线代码分析能力，帮助更好地理解和维护复杂的Unity项目。
