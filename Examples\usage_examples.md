# Unity Symbol Analyzer - Usage Examples

This document provides practical examples of how to use the Unity Symbol Analyzer tool.

## Example 1: Analyzing a Method Call

Suppose you have a Unity script with a method call and want to find all references to it:

```csharp
// PlayerController.cs, line 25, column 10
public void Jump()
{
    // Method implementation
}
```

**Command:**
```bash
dotnet run -- -s "MyUnityProject.sln" -f "Assets/Scripts/PlayerController.cs" -l 25 -c 10
```

**Expected Output:**
```
Symbol: Jump
Kind: Method
Definition: Assets/Scripts/PlayerController.cs:25:5
Reference Count: 5

References:
  Assets/Scripts/PlayerController.cs:25:5 - public void Jump()
  Assets/Scripts/InputHandler.cs:15:20 - player.Jump();
  Assets/Scripts/AIController.cs:30:15 - controller.Jump();
  Assets/Scripts/GameManager.cs:45:12 - playerRef.Jump();
  Assets/Scripts/Tutorial.cs:60:8 - demoPlayer.Jump();
```

## Example 2: Finding Variable References

To analyze a field or property:

```csharp
// GameManager.cs, line 10, column 20
public float gameSpeed = 1.0f;
```

**Command:**
```bash
dotnet run -- -s "Game.sln" -f "Scripts/GameManager.cs" -l 10 -c 20 -v
```

**Expected Output (Verbose):**
```
Analyzing solution: Game.sln
Target location: Scripts/GameManager.cs:10:20
Loaded solution with 3 projects

Symbol: gameSpeed
Kind: Field
Definition: Scripts/GameManager.cs:10:13
Reference Count: 8
Namespace: MyGame.Core
ContainingType: GameManager

References:
  Scripts/GameManager.cs:10:13 - public float gameSpeed = 1.0f;
  Scripts/GameManager.cs:25:15 - Time.timeScale = gameSpeed;
  Scripts/UIManager.cs:40:30 - speedSlider.value = manager.gameSpeed;
  Scripts/SaveSystem.cs:55:25 - data.speed = gameSpeed;
  Scripts/OptionsMenu.cs:70:20 - gameManager.gameSpeed = value;
  Scripts/PowerUp.cs:35:10 - originalSpeed = gameSpeed;
  Scripts/SlowMotion.cs:20:5 - gameSpeed *= 0.5f;
  Scripts/ResetGame.cs:15:8 - gameSpeed = 1.0f;
```

## Example 3: JSON Output for Integration

For programmatic use or integration with other tools:

**Command:**
```bash
dotnet run -- -s "Project.sln" -f "Assets/Player.cs" -l 15 -c 8 -o json
```

**Expected Output:**
```json
{
  "SymbolName": "health",
  "SymbolKind": "Field",
  "DefinitionLocation": "Assets/Player.cs:15:5",
  "ReferenceCount": 12,
  "References": [
    "Assets/Player.cs:15:5 - private int health = 100;",
    "Assets/Player.cs:30:8 - health -= damage;",
    "Assets/Player.cs:45:12 - return health;",
    "Assets/HealthBar.cs:20:25 - bar.fillAmount = player.health / 100f;",
    "Assets/SaveGame.cs:35:15 - data.playerHealth = health;",
    "Assets/GameOver.cs:10:8 - if (health <= 0)",
    "Assets/Powerup.cs:25:5 - health = Mathf.Min(health + 20, 100);",
    "Assets/DamageSystem.cs:40:10 - target.health -= amount;",
    "Assets/HealingPotion.cs:15:5 - player.health += healAmount;",
    "Assets/StatusUI.cs:50:30 - healthText.text = health.ToString();",
    "Assets/Respawn.cs:20:5 - health = 100;",
    "Assets/CheatCodes.cs:60:5 - health = 999;"
  ],
  "Documentation": null,
  "Namespace": "Game.Player",
  "ContainingType": "PlayerController"
}
```

## Example 4: Analyzing Class Definitions

To find all references to a class:

```csharp
// Enemy.cs, line 5, column 15
public class Enemy : MonoBehaviour
```

**Command:**
```bash
dotnet run -- -s "RPG.sln" -f "Assets/Scripts/Enemy.cs" -l 5 -c 15
```

**Expected Output:**
```
Symbol: Enemy
Kind: NamedType
Definition: Assets/Scripts/Enemy.cs:5:14
Reference Count: 25

References include:
- Variable declarations: Enemy currentEnemy;
- Method parameters: void AttackEnemy(Enemy target)
- Generic constraints: where T : Enemy
- Inheritance: class Boss : Enemy
- Array/List declarations: List<Enemy> enemies;
```

## Example 5: Analyzing Unity-Specific Code

Unity components and methods:

```csharp
// PlayerMovement.cs, line 20, column 10
void Update()
{
    // Unity Update method
}
```

**Command:**
```bash
dotnet run -- -s "UnityGame.sln" -f "Assets/PlayerMovement.cs" -l 20 -c 10
```

**Expected Output:**
```
Symbol: Update
Kind: Method
Definition: Assets/PlayerMovement.cs:20:10
Reference Count: 1

Note: Unity's Update() method is called by the engine automatically.
Only explicit calls from your code are counted as references.
```

## Example 6: Batch Analysis Script

For analyzing multiple symbols, you can create a batch script:

```powershell
# analyze_symbols.ps1
$symbols = @(
    @{file="Assets/Player.cs"; line=10; column=15},
    @{file="Assets/Enemy.cs"; line=25; column=20},
    @{file="Assets/GameManager.cs"; line=5; column=25}
)

foreach ($symbol in $symbols) {
    Write-Host "Analyzing $($symbol.file):$($symbol.line):$($symbol.column)"
    dotnet run -- -s "MyGame.sln" -f $symbol.file -l $symbol.line -c $symbol.column -o json
    Write-Host "---"
}
```

## Tips for Effective Usage

1. **Use Verbose Mode**: Add `-v` flag when troubleshooting or learning about the analysis process

2. **JSON for Automation**: Use `-o json` when integrating with other tools or scripts

3. **Relative Paths**: File paths should be relative to the solution directory

4. **Line/Column Numbers**: Use 1-based indexing (first line is 1, first column is 1)

5. **Symbol Positioning**: Click on the symbol in your IDE to get exact line/column numbers

6. **Large Projects**: For large Unity projects, analysis may take a few seconds to complete

## Common Use Cases

- **Refactoring**: Find all usages before renaming or removing code
- **Code Review**: Understand the impact of changes
- **Documentation**: Generate usage reports for APIs
- **Debugging**: Trace where variables or methods are used
- **Code Quality**: Identify unused or overused symbols
