# Build script for Unity Symbol Analyzer

param(
    [string]$Configuration = "Release",
    [string]$OutputPath = "./bin",
    [switch]$Clean,
    [switch]$Publish
)

Write-Host "Unity Symbol Analyzer Build Script" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Clean if requested
if ($Clean) {
    Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
    if (Test-Path $OutputPath) {
        Remove-Item $OutputPath -Recurse -Force
    }
    dotnet clean
}

# Restore packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore

if ($LASTEXITCODE -ne 0) {
    Write-Error "Package restore failed"
    exit 1
}

# Build
Write-Host "Building project..." -ForegroundColor Yellow
dotnet build --configuration $Configuration --no-restore

if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed"
    exit 1
}

# Publish if requested
if ($Publish) {
    Write-Host "Publishing application..." -ForegroundColor Yellow
    
    $publishPath = Join-Path $OutputPath "publish"
    
    # Publish for current platform
    dotnet publish --configuration $Configuration --output $publishPath --no-build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build and publish completed successfully!" -ForegroundColor Green
        Write-Host "Published to: $publishPath" -ForegroundColor Cyan
        
        # Create a simple run script
        $runScript = @"
@echo off
dotnet UnitySymbolAnalyzer.dll %*
"@
        $runScript | Out-File -FilePath (Join-Path $publishPath "analyze.bat") -Encoding ASCII
        
        Write-Host "Created analyze.bat for easy execution" -ForegroundColor Cyan
    } else {
        Write-Error "Publish failed"
        exit 1
    }
} else {
    Write-Host "Build completed successfully!" -ForegroundColor Green
    Write-Host "To run: dotnet run -- [options]" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Example usage:" -ForegroundColor Yellow
Write-Host "  dotnet run -- -s ""MyProject.sln"" -f ""Assets/Scripts/Test.cs"" -l 10 -c 5" -ForegroundColor Cyan
