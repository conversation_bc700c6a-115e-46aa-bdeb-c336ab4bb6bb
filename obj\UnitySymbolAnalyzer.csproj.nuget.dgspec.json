{"format": 1, "restore": {"E:\\workspace\\cs-language-server\\UnitySymbolAnalyzer.csproj": {}}, "projects": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj", "projectName": "OmniSharp.Abstractions", "projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\github\\omnisharp-roslyn\\bin\\obj\\OmniSharp.Abstractions\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "CentralPackageTransitivePinningEnabled": true, "fallbackFolders": ["e:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["E:\\github\\omnisharp-roslyn\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net6.0", "netstandard2.0"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/azure-public/vside/_packaging/vs-impl/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet-tools/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6-transport/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6/nuget/v3/index.json": {}, "https://www.myget.org/F/omnisharp/api/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "net472": {"targetAlias": "net472", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1701", "NU1901", "NU1903", "NU5104"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )", "versionCentrallyManaged": true}, "SQLitePCLRaw.bundle_green": {"suppressParent": "All", "target": "Package", "version": "[2.1.11, )", "versionCentrallyManaged": true}, "System.Collections.Immutable": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "System.Composition": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )", "versionCentrallyManaged": true}, "SQLitePCLRaw.bundle_green": {"suppressParent": "All", "target": "Package", "version": "[2.1.11, )", "versionCentrallyManaged": true}, "System.Collections.Immutable": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "System.Composition": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )", "versionCentrallyManaged": true}, "SQLitePCLRaw.bundle_green": {"suppressParent": "All", "target": "Package", "version": "[2.1.11, )", "versionCentrallyManaged": true}, "System.Collections.Immutable": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "System.Composition": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}}}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.DotNetTest\\OmniSharp.DotNetTest.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.DotNetTest\\OmniSharp.DotNetTest.csproj", "projectName": "OmniSharp.DotNetTest", "projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.DotNetTest\\OmniSharp.DotNetTest.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\github\\omnisharp-roslyn\\bin\\obj\\OmniSharp.DotNetTest\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "CentralPackageTransitivePinningEnabled": true, "fallbackFolders": ["e:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["E:\\github\\omnisharp-roslyn\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/azure-public/vside/_packaging/vs-impl/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet-tools/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6-transport/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6/nuget/v3/index.json": {}, "https://www.myget.org/F/omnisharp/api/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj"}}}, "net472": {"targetAlias": "net472", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1701", "NU1901", "NU1903", "NU5104"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.TestPlatform.ObjectModel": {"target": "Package", "version": "[17.14.0-preview-25107-01, )", "versionCentrallyManaged": true}, "Microsoft.TestPlatform.TranslationLayer": {"target": "Package", "version": "[17.14.0-preview-25107-01, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "dependencies": {"Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.TestPlatform.TranslationLayer": {"target": "Package", "version": "[17.14.0-preview-25107-01, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}}}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Host\\OmniSharp.Host.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Host\\OmniSharp.Host.csproj", "projectName": "OmniSharp.Host", "projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Host\\OmniSharp.Host.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\github\\omnisharp-roslyn\\bin\\obj\\OmniSharp.Host\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "CentralPackageTransitivePinningEnabled": true, "fallbackFolders": ["e:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["E:\\github\\omnisharp-roslyn\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/azure-public/vside/_packaging/vs-impl/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet-tools/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6-transport/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6/nuget/v3/index.json": {}, "https://www.myget.org/F/omnisharp/api/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj"}}}, "net472": {"targetAlias": "net472", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1701", "NU1901", "NU1903", "NU5104"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"McMaster.Extensions.CommandLineUtils": {"target": "Package", "version": "[4.1.0, )", "versionCentrallyManaged": true}, "Microsoft.Build.Locator": {"target": "Package", "version": "[1.6.10, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Configuration.CommandLine": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.DependencyModel": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.FileSystemGlobbing": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "SQLitePCLRaw.bundle_green": {"suppressParent": "All", "target": "Package", "version": "[2.1.11, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "dependencies": {"McMaster.Extensions.CommandLineUtils": {"target": "Package", "version": "[4.1.0, )", "versionCentrallyManaged": true}, "Microsoft.Build.Locator": {"target": "Package", "version": "[1.6.10, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Configuration.CommandLine": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.DependencyModel": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.FileSystemGlobbing": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "SQLitePCLRaw.bundle_green": {"suppressParent": "All", "target": "Package", "version": "[2.1.11, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}}}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.LanguageServerProtocol\\OmniSharp.LanguageServerProtocol.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.LanguageServerProtocol\\OmniSharp.LanguageServerProtocol.csproj", "projectName": "OmniSharp.LanguageServerProtocol", "projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.LanguageServerProtocol\\OmniSharp.LanguageServerProtocol.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\github\\omnisharp-roslyn\\bin\\obj\\OmniSharp.LanguageServerProtocol\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "CentralPackageTransitivePinningEnabled": true, "fallbackFolders": ["e:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["E:\\github\\omnisharp-roslyn\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/azure-public/vside/_packaging/vs-impl/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet-tools/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6-transport/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6/nuget/v3/index.json": {}, "https://www.myget.org/F/omnisharp/api/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.DotNetTest\\OmniSharp.DotNetTest.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.DotNetTest\\OmniSharp.DotNetTest.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Host\\OmniSharp.Host.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Host\\OmniSharp.Host.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.MSBuild\\OmniSharp.MSBuild.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.MSBuild\\OmniSharp.MSBuild.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Script\\OmniSharp.Script.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Script\\OmniSharp.Script.csproj"}}}, "net472": {"targetAlias": "net472", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.DotNetTest\\OmniSharp.DotNetTest.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.DotNetTest\\OmniSharp.DotNetTest.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Host\\OmniSharp.Host.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Host\\OmniSharp.Host.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.MSBuild\\OmniSharp.MSBuild.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.MSBuild\\OmniSharp.MSBuild.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Script\\OmniSharp.Script.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Script\\OmniSharp.Script.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1701", "NU1901", "NU1903", "NU5104"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"OmniSharp.Extensions.LanguageServer": {"target": "Package", "version": "[0.19.9, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-arm64", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x86", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}, {"name": "Microsoft.NETCore.App.Host.win-arm64", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}, {"name": "Microsoft.NETCore.App.Host.win-x86", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Runtime.win-arm64", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x86", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-arm64", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x86", "version": "[6.0.0-preview.7.21317.1, 6.0.0-preview.7.21317.1]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "dependencies": {"OmniSharp.Extensions.LanguageServer": {"target": "Package", "version": "[0.19.9, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win10-arm64": {"#import": []}, "win7-x64": {"#import": []}, "win7-x86": {"#import": []}}}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.MSBuild\\OmniSharp.MSBuild.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.MSBuild\\OmniSharp.MSBuild.csproj", "projectName": "OmniSharp.MSBuild", "projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.MSBuild\\OmniSharp.MSBuild.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\github\\omnisharp-roslyn\\bin\\obj\\OmniSharp.MSBuild\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "CentralPackageTransitivePinningEnabled": true, "fallbackFolders": ["e:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["E:\\github\\omnisharp-roslyn\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/azure-public/vside/_packaging/vs-impl/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet-tools/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6-transport/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6/nuget/v3/index.json": {}, "https://www.myget.org/F/omnisharp/api/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj"}}}, "net472": {"targetAlias": "net472", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1701", "NU1901", "NU1903", "NU5104"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Build": {"include": "Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[17.3.2, )", "versionCentrallyManaged": true}, "Microsoft.Build.Framework": {"include": "Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[17.3.2, )", "versionCentrallyManaged": true}, "Microsoft.Build.Tasks.Core": {"include": "Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[17.3.2, )", "versionCentrallyManaged": true}, "Microsoft.Build.Utilities.Core": {"include": "Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[17.3.2, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )", "versionCentrallyManaged": true}, "NuGet.ProjectModel": {"target": "Package", "version": "[6.15.0-preview.1.86, )", "versionCentrallyManaged": true}, "NuGet.Versioning": {"target": "Package", "version": "[6.15.0-preview.1.86, )", "versionCentrallyManaged": true}, "System.Threading.Tasks.Dataflow": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "dependencies": {"Microsoft.Build": {"include": "Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[17.3.2, )", "versionCentrallyManaged": true}, "Microsoft.Build.Framework": {"include": "Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[17.3.2, )", "versionCentrallyManaged": true}, "Microsoft.Build.Tasks.Core": {"include": "Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[17.3.2, )", "versionCentrallyManaged": true}, "Microsoft.Build.Utilities.Core": {"include": "Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[17.3.2, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )", "versionCentrallyManaged": true}, "NuGet.ProjectModel": {"target": "Package", "version": "[6.15.0-preview.1.86, )", "versionCentrallyManaged": true}, "NuGet.Versioning": {"target": "Package", "version": "[6.15.0-preview.1.86, )", "versionCentrallyManaged": true}, "System.Threading.Tasks.Dataflow": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}}}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj", "projectName": "OmniSharp.Roslyn.CSharp", "projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\github\\omnisharp-roslyn\\bin\\obj\\OmniSharp.Roslyn.CSharp\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "CentralPackageTransitivePinningEnabled": true, "fallbackFolders": ["e:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["E:\\github\\omnisharp-roslyn\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net6.0", "netstandard2.0"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/azure-public/vside/_packaging/vs-impl/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet-tools/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6-transport/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6/nuget/v3/index.json": {}, "https://www.myget.org/F/omnisharp/api/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj"}}}, "net472": {"targetAlias": "net472", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj"}}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1701", "NU1901", "NU1903", "NU5104"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"ICSharpCode.Decompiler": {"target": "Package", "version": "[9.1.0.7988, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.CSharp.Features": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.CSharp.Workspaces": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "System.Reactive": {"target": "Package", "version": "[6.0.1, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "dependencies": {"ICSharpCode.Decompiler": {"target": "Package", "version": "[9.1.0.7988, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.CSharp.Features": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.CSharp.Workspaces": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "System.Reactive": {"target": "Package", "version": "[6.0.1, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ICSharpCode.Decompiler": {"target": "Package", "version": "[9.1.0.7988, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.CSharp.Features": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.CSharp.Workspaces": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "System.Reactive": {"target": "Package", "version": "[6.0.1, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}}}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj", "projectName": "OmniSharp.Roslyn", "projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn\\OmniSharp.Roslyn.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\github\\omnisharp-roslyn\\bin\\obj\\OmniSharp.Roslyn\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "CentralPackageTransitivePinningEnabled": true, "fallbackFolders": ["e:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["E:\\github\\omnisharp-roslyn\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net6.0", "netstandard2.0"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/azure-public/vside/_packaging/vs-impl/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet-tools/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6-transport/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6/nuget/v3/index.json": {}, "https://www.myget.org/F/omnisharp/api/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj"}}}, "net472": {"targetAlias": "net472", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj"}}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1701", "NU1901", "NU1903", "NU5104"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.CodeAnalysis.Common": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.Workspaces.Common": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "System.ComponentModel.Composition": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "dependencies": {"Microsoft.CodeAnalysis.Common": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.Workspaces.Common": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "System.ComponentModel.Composition": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.CodeAnalysis.Common": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.Workspaces.Common": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "System.ComponentModel.Composition": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}}}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Script\\OmniSharp.Script.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Script\\OmniSharp.Script.csproj", "projectName": "OmniSharp.<PERSON>", "projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Script\\OmniSharp.Script.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\github\\omnisharp-roslyn\\bin\\obj\\OmniSharp.Script\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "CentralPackageTransitivePinningEnabled": true, "fallbackFolders": ["e:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["E:\\github\\omnisharp-roslyn\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/azure-public/vside/_packaging/vs-impl/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet-tools/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6-transport/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6/nuget/v3/index.json": {}, "https://www.myget.org/F/omnisharp/api/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj"}}}, "net472": {"targetAlias": "net472", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1701", "NU1901", "NU1903", "NU5104"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Dotnet.Script.DependencyModel": {"target": "Package", "version": "[1.5.0, )", "versionCentrallyManaged": true}, "Dotnet.Script.DependencyModel.NuGet": {"target": "Package", "version": "[1.5.0, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.CSharp.Scripting": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "dependencies": {"Dotnet.Script.DependencyModel": {"target": "Package", "version": "[1.5.0, )", "versionCentrallyManaged": true}, "Dotnet.Script.DependencyModel.NuGet": {"target": "Package", "version": "[1.5.0, )", "versionCentrallyManaged": true}, "Microsoft.CodeAnalysis.CSharp.Scripting": {"target": "Package", "version": "[5.0.0-2.25404.8, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}}}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj", "projectName": "OmniSharp.Shared", "projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Shared\\OmniSharp.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\github\\omnisharp-roslyn\\bin\\obj\\OmniSharp.Shared\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "CentralPackageTransitivePinningEnabled": true, "fallbackFolders": ["e:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["E:\\github\\omnisharp-roslyn\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net6.0", "netstandard2.0"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/azure-public/vside/_packaging/vs-impl/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet-tools/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6-transport/nuget/v3/index.json": {}, "https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet6/nuget/v3/index.json": {}, "https://www.myget.org/F/omnisharp/api/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}}}, "net472": {"targetAlias": "net472", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Abstractions\\OmniSharp.Abstractions.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1701", "NU1901", "NU1903", "NU5104"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.FileProviders.Physical": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.FileSystemGlobbing": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "System.Collections.Immutable": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "System.Composition": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.FileProviders.Physical": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.FileSystemGlobbing": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "System.Collections.Immutable": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "System.Composition": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.FileProviders.Physical": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.FileSystemGlobbing": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "System.Collections.Immutable": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "System.Composition": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"BenchmarkDotNet": "0.13.10", "BenchmarkDotNet.Diagnostics.Windows": "0.13.10", "Cake.Scripting.Transport": "0.16.0", "DiffPlex": "1.8.0", "Dotnet.Script.DependencyModel": "1.5.0", "Dotnet.Script.DependencyModel.NuGet": "1.5.0", "ICSharpCode.Decompiler": "9.1.0.7988", "McMaster.Extensions.CommandLineUtils": "4.1.0", "Microsoft.Build": "17.3.2", "Microsoft.Build.Framework": "17.3.2", "Microsoft.Build.Locator": "1.6.10", "Microsoft.Build.Tasks.Core": "17.3.2", "Microsoft.Build.Utilities.Core": "17.3.2", "Microsoft.CodeAnalysis.Common": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.AspNetCore": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.ExternalAccess.OmniSharp.CSharp": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Features": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Scripting": "5.0.0-2.25404.8", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-2.25404.8", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.IO.Redist": "6.1.0", "Microsoft.NET.Test.Sdk": "17.14.0-preview-25107-01", "Microsoft.SourceLink.GitHub": "8.0.0", "Microsoft.TestPlatform.ObjectModel": "17.14.0-preview-25107-01", "Microsoft.TestPlatform.TranslationLayer": "17.14.0-preview-25107-01", "Newtonsoft.Json": "13.0.3", "NuGet.Common": "6.15.0-preview.1.86", "NuGet.Configuration": "6.15.0-preview.1.86", "NuGet.DependencyResolver.Core": "6.15.0-preview.1.86", "NuGet.Frameworks": "6.15.0-preview.1.86", "NuGet.LibraryModel": "6.15.0-preview.1.86", "NuGet.Packaging": "6.15.0-preview.1.86", "NuGet.ProjectModel": "6.15.0-preview.1.86", "NuGet.Protocol": "6.15.0-preview.1.86", "NuGet.Versioning": "6.15.0-preview.1.86", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol.Testing": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "SQLitePCLRaw.bundle_green": "2.1.11", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Memory": "4.6.0", "System.Reactive": "6.0.1", "System.Reflection.Metadata": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "9.0.0", "System.ValueTuple": "4.5.0", "xunit": "2.8.1", "xunit.abstractions": "2.0.3", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}}}, "E:\\workspace\\cs-language-server\\UnitySymbolAnalyzer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\workspace\\cs-language-server\\UnitySymbolAnalyzer.csproj", "projectName": "UnitySymbolAnalyzer", "projectPath": "E:\\workspace\\cs-language-server\\UnitySymbolAnalyzer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\workspace\\cs-language-server\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["e:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Host\\OmniSharp.Host.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Host\\OmniSharp.Host.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.LanguageServerProtocol\\OmniSharp.LanguageServerProtocol.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.LanguageServerProtocol\\OmniSharp.LanguageServerProtocol.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.MSBuild\\OmniSharp.MSBuild.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.MSBuild\\OmniSharp.MSBuild.csproj"}, "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj": {"projectPath": "E:\\github\\omnisharp-roslyn\\src\\OmniSharp.Roslyn.CSharp\\OmniSharp.Roslyn.CSharp.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CommandLineParser": {"target": "Package", "version": "[2.9.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.7.25380.108/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(,4.7.32767]", "Microsoft.NETCore.App": "(,2.1.32767]", "Microsoft.VisualBasic": "(,10.3.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,4.5.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,8.0.32767]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,5.0.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.5.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,8.0.32767]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,8.0.32767]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,5.0.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,4.6.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,4.5.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,8.0.32767]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,4.5.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,4.7.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,8.0.32767]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.7.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,6.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Runtime.WindowsRuntime": "(,4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(,4.7.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,4.6.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,4.4.32767]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,8.0.32767]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,8.0.32767]", "System.Text.Json": "(,8.0.32767]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.Channels": "(,8.0.32767]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,8.0.32767]", "System.Threading.Tasks.Extensions": "(,4.5.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,4.3.32767]"}}}}}}