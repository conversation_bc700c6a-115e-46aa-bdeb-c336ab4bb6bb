# Unity Symbol Analyzer

A C# Language Server tool for analyzing Unity projects offline. This tool can load Unity .sln files and provide symbol analysis including reference counting for any symbol at a specific file location.

## Features

- **Offline Analysis**: Load and analyze Unity .sln files without requiring Unity Editor
- **Symbol Location**: Find symbols at specific file positions (line/column)
- **Reference Counting**: Count all references to a symbol across the entire solution
- **Multiple Output Formats**: Support for both human-readable text and JSON output
- **Cross-Project Analysis**: Analyze symbols across multiple projects in a solution

## Requirements

- .NET 8.0 or later
- Unity project with .sln file generated

## Installation

1. Clone this repository
2. Build the project:
   ```bash
   dotnet build
   ```

## Usage

### Basic Usage

```bash
dotnet run -- -s "path/to/Unity.sln" -f "Assets/Scripts/MyScript.cs" -l 10 -c 15
```

### Command Line Options

- `-s, --solution`: Path to Unity .sln file (required)
- `-f, --file`: Path to C# file relative to solution (required)
- `-l, --line`: Line number (1-based, required)
- `-c, --column`: Column number (1-based, required)
- `-v, --verbose`: Enable verbose output (optional)
- `-o, --output`: Output format - "text" or "json" (optional, default: "text")

### Examples

#### Analyze a method call
```bash
dotnet run -- -s "C:/Unity/MyProject/MyProject.sln" -f "Assets/Scripts/PlayerController.cs" -l 25 -c 10 -v
```

#### Get JSON output for integration
```bash
dotnet run -- -s "MyProject.sln" -f "Scripts/GameManager.cs" -l 15 -c 20 -o json
```

### Sample Output

#### Text Format
```
Symbol: Update
Kind: Method
Definition: Assets/Scripts/PlayerController.cs:20:5
Reference Count: 3
```

#### JSON Format
```json
{
  "SymbolName": "Update",
  "SymbolKind": "Method",
  "DefinitionLocation": "Assets/Scripts/PlayerController.cs:20:5",
  "ReferenceCount": 3,
  "References": [
    "Assets/Scripts/PlayerController.cs:20:5 - void Update()",
    "Assets/Scripts/EnemyController.cs:15:10 - player.Update();",
    "Assets/Scripts/GameLoop.cs:30:8 - controller.Update();"
  ],
  "Namespace": "MyGame.Controllers",
  "ContainingType": "PlayerController"
}
```

## How It Works

1. **Solution Parsing**: Parses the Unity .sln file to extract all C# projects
2. **Roslyn Analysis**: Uses Microsoft.CodeAnalysis (Roslyn) to build semantic models
3. **Symbol Resolution**: Locates the symbol at the specified file position
4. **Reference Finding**: Searches across all projects for symbol references
5. **Result Aggregation**: Counts and formats the results

## Supported Symbol Types

- Classes, Interfaces, Structs
- Methods, Properties, Fields
- Local variables and parameters
- Namespaces and using directives
- Generic types and methods

## Limitations

- Requires generated .sln file (use Unity's "Open C# Project" to generate)
- Analysis is based on source code only (no runtime analysis)
- Some Unity-specific attributes may not be fully resolved

## Troubleshooting

### Common Issues

1. **"Solution file not found"**: Ensure the .sln file path is correct and the file exists
2. **"File not found in solution"**: Check that the file path is relative to the solution directory
3. **"No symbol found"**: Verify the line/column numbers point to a valid symbol

### Verbose Mode

Use the `-v` flag to get detailed information about the analysis process:

```bash
dotnet run -- -s "MyProject.sln" -f "Scripts/Test.cs" -l 1 -c 1 -v
```

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is open source and available under the MIT License.
